import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  UserGroupIcon,
  ChartBarIcon,
  HeartIcon,
  SparklesIcon,
  ArrowRightIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';
import LandingIllustration from '../components/ui/LandingIllustration';
import Navbar from '../components/layout/Navbar';
import AnimatedSection from '../components/ui/AnimatedSection';
import CountUp from '../components/ui/CountUp';
import Chatbot from '../components/ui/Chatbot';

const LandingPage = () => {
  const navigate = useNavigate();

  const hrBenefits = [
    {
      icon: UserGroupIcon,
      title: "Appariement Intelligent d'Employés",
      description: "Créez des connexions significatives entre départements, équipes et sites grâce à notre algorithme de correspondance intelligent."
    },
    {
      icon: ChartBarIcon,
      title: "Analyses de Campagne",
      description: "Suivez l'engagement, mesurez le succès des connexions et obtenez des insights sur vos initiatives de culture d'entreprise."
    },
    {
      icon: SparklesIcon,
      title: "Création Facile de Campagnes",
      description: "Lancez des campagnes de rencontres café en quelques minutes avec des modèles personnalisables et une planification automatisée."
    }
  ];

  const testimonials = [
    {
      name: "Sarah Chen",
      role: "Spécialiste Marketing",
      company: "TechCorp",
      quote: "CoffeeMeet m'a aidée à me connecter avec des collègues que je n'aurais jamais rencontrés autrement. J'ai créé de véritables amitiés et appris énormément sur les différents départements !",
      rating: 5
    },
    {
      name: "Marcus Johnson",
      role: "Développeur Logiciel",
      company: "InnovateLab",
      quote: "En tant que travailleur à distance, je me sentais déconnecté. Les rencontres café m'ont donné l'occasion de créer des liens avec mes coéquipiers et de me sentir plus intégré dans la culture d'entreprise.",
      rating: 5
    },
    {
      name: "Elena Rodriguez",
      role: "Coordinatrice RH",
      company: "GrowthCo",
      quote: "Ces discussions café décontractées ont été formidables pour décloisonner les départements. Les gens collaborent davantage et l'énergie au bureau est bien meilleure !",
      rating: 5
    }
  ];

  const stats = [
    { number: 87, label: "Rapportent des relations professionnelles plus fortes" },
    { number: 92, label: "Se sentent plus connectés à la culture d'entreprise" },
    { number: 78, label: "Collaboration inter-équipes accrue" },
    { number: 95, label: "Recommanderaient à leurs collègues" }
  ];

  return (
    <div className="min-h-screen bg-cream">
      {/* Navigation Bar */}
      <Navbar />

      {/* Hero Section */}
      <div className="relative overflow-hidden pt-24 lg:pt-32">
        <div className="flex min-h-screen">
          {/* Left side - Content */}
          <div className="flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8">
            <div className="max-w-2xl w-full space-y-8 text-center lg:text-left">


              {/* Hero Content */}
              <AnimatedSection animation="fadeInUp" delay={400}>
                <div className="space-y-8">
                  <h1 className="text-4xl lg:text-5xl xl:text-6xl font-bold text-warmGray-800 leading-tight">
                    Favorisez des relations authentiques et créez une communauté <span className="text-peach-600">d'entreprise</span> florissante
                  </h1>
                  <p className="text-xl lg:text-2xl text-warmGray-600 leading-relaxed">
                    Connectez les employés entre départements, sites et équipes grâce à des campagnes de rencontres café personnalisées.
                    Créez des liens durables qui renforcent l'engagement, la collaboration et la culture d'entreprise.
                  </p>
                </div>
              </AnimatedSection>

              {/* CTA Buttons */}
              <AnimatedSection animation="fadeInUp" delay={600}>
                <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start pt-12">
                  <button
                    onClick={() => navigate('/register')}
                    className="bg-[#E8C4A0] hover:bg-[#DDB892] text-[#8B6F47] font-medium py-5 px-10 rounded-full transition-all duration-200 transform hover:scale-[1.02] flex items-center justify-center gap-2 text-lg"
                  >
                    Commencer Gratuitement
                    <ArrowRightIcon className="h-5 w-5" />
                  </button>
                  <button
                    onClick={() => navigate('/login')}
                    className="border-2 border-warmGray-400 text-warmGray-700 hover:bg-warmGray-50 font-medium py-5 px-10 rounded-full transition-all duration-200 transform hover:scale-[1.02] text-lg"
                  >
                    Se Connecter
                  </button>
                </div>
              </AnimatedSection>


            </div>
          </div>

          {/* Right side - Illustration */}
          <div className="hidden lg:block flex-1 relative min-h-[600px]">
            <AnimatedSection animation="fadeInRight" delay={400}>
              <LandingIllustration />
            </AnimatedSection>
          </div>
        </div>
      </div>

      {/* About Section */}
      <section id="about" className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-6xl mx-auto">
          <AnimatedSection animation="fadeInUp">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-warmGray-800 mb-6">
                Créer des connexions qui comptent
              </h2>
              <p className="text-xl text-warmGray-600 max-w-3xl mx-auto leading-relaxed">
                Nous croyons que des relations professionnelles solides sont le fondement d'organisations prospères.
                CoffeeMeet facilite la création de connexions significatives qui renforcent l'engagement, la collaboration et la culture d'entreprise.
              </p>
            </div>
          </AnimatedSection>

          {/* Stats Grid */}
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
            {stats.map((stat, index) => (
              <AnimatedSection key={index} animation="scaleIn" delay={index * 100}>
                <div className="text-center">
                  <CountUp
                    end={stat.number}
                    duration={5000}
                    suffix="%"
                    delay={index * 500}
                    className="text-4xl font-bold text-peach-600 mb-2"
                  />
                  <div className="text-sm text-warmGray-600">{stat.label}</div>
                </div>
              </AnimatedSection>
            ))}
          </div>
        </div>
      </section>

      {/* HR Manager Benefits Section */}
      <section id="solutions" className="py-20 px-4 sm:px-6 lg:px-8 bg-peach-50">
        <div className="max-w-6xl mx-auto">
          <AnimatedSection animation="fadeInUp">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-warmGray-800 mb-6">
                Outils puissants pour les responsables RH
              </h2>
              <p className="text-xl text-warmGray-600 max-w-3xl mx-auto">
                Tout ce dont vous avez besoin pour créer, gérer et mesurer des campagnes de connexion professionnelle réussies
              </p>
            </div>
          </AnimatedSection>

          <div className="grid md:grid-cols-3 gap-8">
            {hrBenefits.map((benefit, index) => (
              <AnimatedSection key={index} animation="fadeInUp" delay={index * 200}>
                <div className="bg-white rounded-2xl p-8 shadow-sm hover:shadow-md transition-shadow duration-200">
                  <div className="bg-peach-100 w-16 h-16 rounded-full flex items-center justify-center mb-6">
                    <benefit.icon className="h-8 w-8 text-peach-600" />
                  </div>
                  <h3 className="text-xl font-bold text-warmGray-800 mb-4">{benefit.title}</h3>
                  <p className="text-warmGray-600 leading-relaxed">{benefit.description}</p>
                </div>
              </AnimatedSection>
            ))}
          </div>
        </div>
      </section>

      {/* Employee Testimonials Section */}
      <section id="testimonials" className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-6xl mx-auto">
          <AnimatedSection animation="fadeInUp">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-warmGray-800 mb-6">
                Ce que disent les employés
              </h2>
              <p className="text-xl text-warmGray-600">
                Témoignages authentiques de personnes qui ont vécu le pouvoir des connexions professionnelles
              </p>
            </div>
          </AnimatedSection>

          <div className="grid md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <AnimatedSection key={index} animation="fadeInUp" delay={index * 150}>
                <div className="bg-white rounded-2xl p-8 shadow-sm border border-warmGray-100">
                {/* Rating Stars */}
                <div className="flex mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <svg key={i} className="h-5 w-5 text-yellow-400 fill-current" viewBox="0 0 20 20">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                  ))}
                </div>

                {/* Quote */}
                <blockquote className="text-warmGray-700 mb-6 leading-relaxed">
                  "{testimonial.quote}"
                </blockquote>

                {/* Author */}
                <div className="border-t border-warmGray-100 pt-4">
                  <div className="font-medium text-warmGray-800">{testimonial.name}</div>
                  <div className="text-sm text-warmGray-500">{testimonial.role}</div>
                  <div className="text-sm text-peach-600 font-medium">{testimonial.company}</div>
                </div>
                </div>
              </AnimatedSection>
            ))}
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-peach-100 to-cream">
        <div className="max-w-4xl mx-auto text-center">
          <AnimatedSection animation="fadeInUp">
            <div className="mb-8">
              <HeartIcon className="h-16 w-16 text-peach-600 mx-auto mb-6" />
              <h2 className="text-4xl font-bold text-warmGray-800 mb-6">
                Prêt à transformer votre culture d'entreprise ?
              </h2>
              <p className="text-xl text-warmGray-600 mb-8 leading-relaxed">
                Rejoignez des centaines d'entreprises qui utilisent CoffeeMeet pour créer des équipes plus fortes et connectées.
                Commencez à créer des relations significatives dès aujourd'hui.
              </p>
            </div>
          </AnimatedSection>

          {/* Benefits List */}
          <AnimatedSection animation="fadeInUp" delay={200}>
            <div className="grid md:grid-cols-2 gap-4 mb-12 text-left max-w-2xl mx-auto">
              {[
                "Configurez votre première campagne en moins de 5 minutes",
                "Algorithme de correspondance intelligent pour des connexions significatives",
                "Analyses complètes et suivi de l'engagement",
                "Intégration transparente avec votre flux de travail existant"
              ].map((benefit, index) => (
                <div key={index} className="flex items-center gap-3">
                  <CheckCircleIcon className="h-6 w-6 text-peach-600 flex-shrink-0" />
                  <span className="text-warmGray-700">{benefit}</span>
                </div>
              ))}
            </div>
          </AnimatedSection>

          {/* Final CTA Buttons */}
          <AnimatedSection animation="fadeInUp" delay={400}>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                onClick={() => navigate('/register')}
                className="bg-[#E8C4A0] hover:bg-[#DDB892] text-[#8B6F47] font-medium py-4 px-8 rounded-full transition-all duration-200 transform hover:scale-[1.02] flex items-center justify-center gap-2 text-lg"
              >
                Commencer à Créer des Connexions
                <ArrowRightIcon className="h-5 w-5" />
              </button>
              <button
                onClick={() => navigate('/login')}
                className="border-2 border-warmGray-400 text-warmGray-700 hover:bg-white font-medium py-4 px-8 rounded-full transition-all duration-200 transform hover:scale-[1.02] text-lg"
              >
                Vous avez déjà un compte ?
              </button>
            </div>
          </AnimatedSection>

          {/* Contact Info */}
          <AnimatedSection animation="fadeIn" delay={600}>
            <div className="mt-12 pt-8 border-t border-warmGray-200">
              <p className="text-sm text-warmGray-500">
                Des questions ? Nous sommes là pour vous aider. Contactez-nous à{' '}
                <a href="mailto:<EMAIL>" className="text-peach-600 hover:text-peach-700 font-medium">
                  <EMAIL>
                </a>
              </p>
            </div>
          </AnimatedSection>
        </div>
      </section>

      {/* Chatbot Widget */}
      <Chatbot />
    </div>
  );
};

export default LandingPage;
